import type { Address } from 'abitype';
import type { Account } from '../../accounts/types.js';
import type { Client } from '../../clients/createClient.js';
import type { Transport } from '../../clients/transports/createTransport.js';
import type { ErrorType } from '../../errors/utils.js';
import type { Chain } from '../../types/chain.js';
import { type ChecksumAddressErrorType } from '../../utils/address/getAddress.js';
import type { RequestErrorType } from '../../utils/buildRequest.js';
export type GetAddressesReturnType = Address[];
export type GetAddressesErrorType = RequestErrorType | ChecksumAddressErrorType | ErrorType;
/**
 * Returns a list of account addresses owned by the wallet or client.
 *
 * - Docs: https://viem.sh/docs/actions/wallet/getAddresses.html
 * - JSON-RPC Methods: [`eth_accounts`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_accounts)
 *
 * @param client - Client to use
 * @returns List of account addresses owned by the wallet or client. {@link GetAddressesReturnType}
 *
 * @example
 * import { createWalletClient, custom } from 'viem'
 * import { mainnet } from 'viem/chains'
 * import { getAddresses } from 'viem/wallet'
 *
 * const client = createWalletClient({
 *   chain: mainnet,
 *   transport: custom(window.ethereum),
 * })
 * const accounts = await getAddresses(client)
 */
export declare function getAddresses<TChain extends Chain | undefined, TAccount extends Account | undefined = undefined>(client: Client<Transport, TChain, TAccount>): Promise<GetAddressesReturnType>;
//# sourceMappingURL=getAddresses.d.ts.map