import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const fantom = /*#__PURE__*/ defineChain({
  id: 250,
  name: '<PERSON><PERSON>',
  network: 'fantom',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: 'FTM',
  },
  rpcUrls: {
    default: { http: ['https://rpc.ankr.com/fantom'] },
    public: { http: ['https://rpc.ankr.com/fantom'] },
  },
  blockExplorers: {
    etherscan: { name: 'FTMScan', url: 'https://ftmscan.com' },
    default: { name: 'FTMScan', url: 'https://ftmscan.com' },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 33001987,
    },
  },
})
