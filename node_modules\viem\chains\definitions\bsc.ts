import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const bsc = /*#__PURE__*/ define<PERSON>hain({
  id: 56,
  name: 'BNB Smart Chain',
  network: 'bsc',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON><PERSON>',
    symbol: 'BNB',
  },
  rpcUrls: {
    default: { http: ['https://rpc.ankr.com/bsc'] },
    public: { http: ['https://rpc.ankr.com/bsc'] },
  },
  blockExplorers: {
    etherscan: { name: 'BscScan', url: 'https://bscscan.com' },
    default: { name: 'BscScan', url: 'https://bscscan.com' },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 15921452,
    },
  },
})
