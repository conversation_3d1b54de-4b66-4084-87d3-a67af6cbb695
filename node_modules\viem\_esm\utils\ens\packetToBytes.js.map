{"version": 3, "file": "packetToBytes.js", "sourceRoot": "", "sources": ["../../../utils/ens/packetToBytes.ts"], "names": [], "mappings": "AAGA,OAAO,EAEL,aAAa,GACd,MAAM,wBAAwB,CAAA;AAC/B,OAAO,EAEL,eAAe,GAChB,MAAM,sBAAsB,CAAA;AAC7B,OAAO,EAA2B,SAAS,EAAE,MAAM,gBAAgB,CAAA;AAQnE;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,MAAc;IAC1C,iCAAiC;IACjC,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;IAC7C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAA;IAEhD,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;IAEjE,IAAI,MAAM,GAAG,CAAC,CAAA;IACd,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,IAAI,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QACpC,mEAAmE;QACnE,iDAAiD;QACjD,IAAI,OAAO,CAAC,UAAU,GAAG,GAAG;YAC1B,OAAO,GAAG,aAAa,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAC9D,KAAK,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAA;QAC9B,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,GAAG,CAAC,CAAC,CAAA;QAC9B,MAAM,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,CAAA;KAC7B;IAED,IAAI,KAAK,CAAC,UAAU,KAAK,MAAM,GAAG,CAAC;QAAE,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAA;IAEtE,OAAO,KAAK,CAAA;AACd,CAAC"}