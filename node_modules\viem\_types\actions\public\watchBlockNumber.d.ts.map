{"version": 3, "file": "watchBlockNumber.d.ts", "sourceRoot": "", "sources": ["../../../actions/public/watchBlockNumber.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,+BAA+B,CAAA;AAC3D,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,6CAA6C,CAAA;AAC5E,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAA;AACtD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,sBAAsB,CAAA;AACjD,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAA;AAIlE,OAAO,EAAE,KAAK,aAAa,EAAQ,MAAM,qBAAqB,CAAA;AAG9D,OAAO,EACL,KAAK,wBAAwB,EAE9B,MAAM,qBAAqB,CAAA;AAE5B,MAAM,MAAM,sBAAsB,GAAG,wBAAwB,CAAA;AAC7D,MAAM,MAAM,eAAe,GAAG,CAC5B,WAAW,EAAE,sBAAsB,EACnC,eAAe,EAAE,sBAAsB,GAAG,SAAS,KAChD,IAAI,CAAA;AAET,MAAM,MAAM,WAAW,GAAG;IACxB,uEAAuE;IACvE,UAAU,CAAC,EAAE,OAAO,CAAA;IACpB,kGAAkG;IAClG,WAAW,CAAC,EAAE,OAAO,CAAA;IACrB,8EAA8E;IAC9E,eAAe,CAAC,EAAE,MAAM,CAAA;CACzB,CAAA;AAED,MAAM,MAAM,0BAA0B,CACpC,UAAU,SAAS,SAAS,GAAG,SAAS,IACtC;IACF,gEAAgE;IAChE,aAAa,EAAE,eAAe,CAAA;IAC9B,sFAAsF;IACtF,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAA;CACjC,GAAG,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,SAAS,WAAW,GAEvD;IACE,UAAU,CAAC,EAAE,KAAK,CAAA;IAClB,WAAW,CAAC,EAAE,KAAK,CAAA;IACnB,0GAA0G;IAC1G,IAAI,CAAC,EAAE,KAAK,CAAA;IACZ,eAAe,CAAC,EAAE,KAAK,CAAA;CACxB,GACD,CAAC,WAAW,GAAG;IAAE,IAAI,EAAE,IAAI,CAAA;CAAE,CAAC,GAClC,WAAW,GAAG;IAAE,IAAI,CAAC,EAAE,IAAI,CAAA;CAAE,CAAC,CAAA;AAElC,MAAM,MAAM,0BAA0B,GAAG,MAAM,IAAI,CAAA;AAEnD,MAAM,MAAM,yBAAyB,GAAG,aAAa,GAAG,SAAS,CAAA;AAEjE;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,wBAAgB,gBAAgB,CAC9B,MAAM,SAAS,KAAK,GAAG,SAAS,EAChC,UAAU,SAAS,SAAS,EAE5B,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,EAClC,EACE,WAAmB,EACnB,UAAkB,EAClB,aAAa,EACb,OAAO,EACP,IAAI,EAAE,KAAK,EACX,eAAwC,GACzC,EAAE,0BAA0B,CAAC,UAAU,CAAC,GACxC,0BAA0B,CAqF5B"}