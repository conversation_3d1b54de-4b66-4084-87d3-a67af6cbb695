{"version": 3, "file": "signTypedData.d.ts", "sourceRoot": "", "sources": ["../../../accounts/utils/signTypedData.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,SAAS,CAAA;AAExC,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,qBAAqB,CAAA;AAC9C,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,0BAA0B,CAAA;AACnE,OAAO,EACL,KAAK,sBAAsB,EAG5B,MAAM,wCAAwC,CAAA;AAC/C,OAAO,EACL,KAAK,uBAAuB,EAE7B,MAAM,yCAAyC,CAAA;AAEhD,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAA;AACtD,OAAO,EAAE,KAAK,aAAa,EAAQ,MAAM,WAAW,CAAA;AAEpD,MAAM,MAAM,uBAAuB,CACjC,UAAU,SAAS,SAAS,GAAG;IAAE,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAA;CAAE,GAAG,SAAS,EACrE,YAAY,SAAS,MAAM,GAAG,MAAM,IAClC,mBAAmB,CAAC,UAAU,EAAE,YAAY,CAAC,GAAG;IAClD,oCAAoC;IACpC,UAAU,EAAE,GAAG,CAAA;CAChB,CAAA;AAED,MAAM,MAAM,uBAAuB,GAAG,GAAG,CAAA;AAEzC,MAAM,MAAM,sBAAsB,GAC9B,sBAAsB,GACtB,aAAa,GACb,uBAAuB,GACvB,SAAS,CAAA;AAEb;;;;;GAKG;AACH,wBAAsB,aAAa,CACjC,KAAK,CAAC,UAAU,SAAS,SAAS,GAAG;IAAE,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAA;CAAE,EAC/D,YAAY,SAAS,MAAM,GAAG,MAAM,EACpC,EACA,UAAU,EACV,GAAG,SAAS,EACb,EAAE,uBAAuB,CACxB,UAAU,EACV,YAAY,CACb,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAMnC"}