import React, { useState } from 'react';
import { Play, AlertTriangle, Calculator } from 'lucide-react';
import { opportunitiesApi, formatCurrency, handleApiError } from '@/lib/api';
import { useContractValidation } from '@/hooks/useContractValidation';
import type { ArbitrageOpportunity, SimulationResult } from '@/types';
import toast from 'react-hot-toast';

interface ExecuteButtonProps {
  opportunity: ArbitrageOpportunity;
  onExecute: (opportunityId: number, amountIn: number) => Promise<void>;
}

export const ExecuteButton: React.FC<ExecuteButtonProps> = ({
  opportunity,
  onExecute,
}) => {
  const [showModal, setShowModal] = useState(false);
  const [amountIn, setAmountIn] = useState(opportunity.minAmount.toString());
  const [simulation, setSimulation] = useState<SimulationResult | null>(null);
  const [isSimulating, setIsSimulating] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);

  const { validateArbitrageExecution, isWalletConnected, contractError } = useContractValidation();

  const handleSimulate = async () => {
    if (!opportunity.id) return;
    
    try {
      setIsSimulating(true);
      const amount = parseFloat(amountIn);
      
      if (amount < opportunity.minAmount || amount > opportunity.maxAmount) {
        toast.error(`Amount must be between ${opportunity.minAmount} and ${opportunity.maxAmount} ETH`);
        return;
      }
      
      const result = await opportunitiesApi.simulate(opportunity.id, amount);
      setSimulation(result);
    } catch (error) {
      toast.error(handleApiError(error));
    } finally {
      setIsSimulating(false);
    }
  };

  const handleExecute = async () => {
    if (!opportunity.id) return;

    try {
      setIsExecuting(true);
      const amount = parseFloat(amountIn);

      // Validate contract and execution parameters
      const validation = validateArbitrageExecution(amount);

      if (!validation.canExecute) {
        toast.error(validation.error || 'Cannot execute trade');
        return;
      }

      if (validation.error) {
        // Show warning but allow execution
        toast.warning(validation.error);
      }

      await onExecute(opportunity.id, amount);
      setShowModal(false);
      setSimulation(null);
    } catch (error) {
      toast.error(handleApiError(error));
    } finally {
      setIsExecuting(false);
    }
  };

  const isAmountValid = () => {
    const amount = parseFloat(amountIn);
    return !isNaN(amount) && amount >= opportunity.minAmount && amount <= opportunity.maxAmount;
  };

  return (
    <>
      <button
        onClick={() => setShowModal(true)}
        className="btn-success btn-sm flex items-center"
        disabled={!opportunity.id || !isWalletConnected}
        title={!isWalletConnected ? 'Connect wallet to execute trades' : ''}
      >
        <Play className="w-4 h-4 mr-1" />
        Execute
      </button>

      {/* Execution Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              Execute Arbitrage
            </h2>

            {/* Contract Error Warning */}
            {contractError && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                <div className="flex items-center">
                  <AlertTriangle className="w-4 h-4 text-red-600 mr-2" />
                  <p className="text-red-800 text-sm font-medium">Contract Error</p>
                </div>
                <p className="text-red-700 text-sm mt-1">{contractError}</p>
              </div>
            )}

            {/* Wallet Connection Warning */}
            {!isWalletConnected && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                <div className="flex items-center">
                  <AlertTriangle className="w-4 h-4 text-yellow-600 mr-2" />
                  <p className="text-yellow-800 text-sm font-medium">Wallet Not Connected</p>
                </div>
                <p className="text-yellow-700 text-sm mt-1">Please connect your wallet to execute trades.</p>
              </div>
            )}

            {/* Opportunity Details */}
            <div className="bg-gray-50 rounded-lg p-4 mb-4">
              <h3 className="font-medium text-gray-900 mb-2">
                {opportunity.symbolA}/{opportunity.symbolB}
              </h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-600">Route</p>
                  <p className="font-medium">{opportunity.dexA} → {opportunity.dexB}</p>
                </div>
                <div>
                  <p className="text-gray-600">Expected Profit</p>
                  <p className="font-medium text-success-600">
                    +{formatCurrency(opportunity.netProfit)} ETH
                  </p>
                </div>
              </div>
            </div>

            {/* Amount Input */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Amount to Trade (ETH)
              </label>
              <input
                type="number"
                value={amountIn}
                onChange={(e) => setAmountIn(e.target.value)}
                min={opportunity.minAmount}
                max={opportunity.maxAmount}
                step="0.01"
                className="input"
                placeholder="Enter amount in ETH"
              />
              <p className="text-xs text-gray-500 mt-1">
                Range: {formatCurrency(opportunity.minAmount, 2)} - {formatCurrency(opportunity.maxAmount, 2)} ETH
              </p>
            </div>

            {/* Simulate Button */}
            <button
              onClick={handleSimulate}
              disabled={!isAmountValid() || isSimulating}
              className="btn-secondary w-full mb-4 flex items-center justify-center"
            >
              <Calculator className="w-4 h-4 mr-2" />
              {isSimulating ? 'Simulating...' : 'Simulate Trade'}
            </button>

            {/* Simulation Results */}
            {simulation && (
              <div className="bg-blue-50 rounded-lg p-4 mb-4">
                <h4 className="font-medium text-blue-900 mb-3">Simulation Results</h4>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-blue-700">Estimated Profit:</span>
                    <span className="font-medium text-success-600">
                      +{formatCurrency(simulation.netProfit)} ETH
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-blue-700">Gas Cost:</span>
                    <span className="font-medium">
                      {formatCurrency(simulation.estimatedGasCost)} ETH
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-blue-700">Flash Loan Fee:</span>
                    <span className="font-medium">
                      {formatCurrency(simulation.flashLoanFee)} ETH
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-blue-700">Success Probability:</span>
                    <span className="font-medium">
                      {(simulation.successProbability * 100).toFixed(1)}%
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-blue-700">Execution Time:</span>
                    <span className="font-medium">{simulation.executionTime}</span>
                  </div>
                </div>

                {/* Warnings */}
                {simulation.warnings.length > 0 && (
                  <div className="mt-3 pt-3 border-t border-blue-200">
                    <div className="flex items-start">
                      <AlertTriangle className="w-4 h-4 text-warning-500 mr-2 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="text-sm font-medium text-warning-700 mb-1">Warnings:</p>
                        <ul className="text-xs text-warning-600 space-y-1">
                          {simulation.warnings.map((warning, index) => (
                            <li key={index}>• {warning}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  setShowModal(false);
                  setSimulation(null);
                }}
                className="btn-secondary flex-1"
                disabled={isExecuting}
              >
                Cancel
              </button>
              
              <button
                onClick={handleExecute}
                disabled={!isAmountValid() || isExecuting || !simulation}
                className="btn-success flex-1 flex items-center justify-center"
              >
                {isExecuting ? (
                  <>
                    <div className="loading-spinner w-4 h-4 mr-2"></div>
                    Executing...
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4 mr-2" />
                    Execute Trade
                  </>
                )}
              </button>
            </div>

            {/* Risk Disclaimer */}
            <div className="mt-4 p-3 bg-warning-50 rounded-lg">
              <div className="flex items-start">
                <AlertTriangle className="w-4 h-4 text-warning-500 mr-2 mt-0.5 flex-shrink-0" />
                <div className="text-xs text-warning-700">
                  <p className="font-medium mb-1">Risk Warning:</p>
                  <p>
                    Arbitrage trading involves risks including price slippage, gas cost fluctuations, 
                    and potential loss of funds. Only trade with amounts you can afford to lose.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
