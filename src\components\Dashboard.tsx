import React, { useState, useEffect } from 'react';
import { Activity, TrendingUp, Zap, AlertTriangle, Wallet, Settings } from 'lucide-react';
import { OpportunityList } from './OpportunityList';
import { TradeHistory } from './TradeHistory';
import { PriceMonitor } from './PriceMonitor';
import { SystemStatus } from './SystemStatus';
import { WalletConnection } from './WalletConnection';
import { useWebSocket } from '@/lib/websocket';
import { useWeb3 } from '@/contexts/Web3Context';
import { useContractValidation } from '@/hooks/useContractValidation';
import { opportunitiesApi, tradesApi, systemApi } from '@/lib/api';
import type { ArbitrageOpportunity, TradeExecution, SystemInfo, OpportunityStats, TradeStats } from '@/types';
import toast from 'react-hot-toast';

export const Dashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'opportunities' | 'trades' | 'prices' | 'system'>('opportunities');
  const [opportunities, setOpportunities] = useState<ArbitrageOpportunity[]>([]);
  const [trades, setTrades] = useState<TradeExecution[]>([]);
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [opportunityStats, setOpportunityStats] = useState<OpportunityStats | null>(null);
  const [tradeStats, setTradeStats] = useState<TradeStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);
  const [wsActivity, setWsActivity] = useState(false);

  const { isConnected, connectionError, socket } = useWebSocket();
  const { walletState, contractError } = useWeb3();
  const { validateArbitrageExecution } = useContractValidation();

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Set up WebSocket event listeners
  useEffect(() => {
    if (!socket) return;

    const handleOpportunitiesUpdate = (data: ArbitrageOpportunity[]) => {
      // Show WebSocket activity
      setWsActivity(true);
      setTimeout(() => setWsActivity(false), 1000);

      // Only update from WebSocket if we have initial data loaded and WebSocket has data
      // This prevents WebSocket from overriding REST API data with empty arrays
      if (initialDataLoaded && data.length > 0) {
        console.log('WebSocket: Updating opportunities with', data.length, 'items');
        setOpportunities(data);
      } else if (!initialDataLoaded) {
        // If initial data not loaded yet, accept WebSocket data
        console.log('WebSocket: Setting initial opportunities with', data.length, 'items');
        setOpportunities(data);
      } else {
        console.log('WebSocket: Ignoring empty opportunities update');
      }
    };

    const handleTradesUpdate = (data: TradeExecution[]) => {
      setWsActivity(true);
      setTimeout(() => setWsActivity(false), 1000);
      setTrades(data);
    };

    const handleNewOpportunity = (opportunity: ArbitrageOpportunity) => {
      toast.success(`New arbitrage opportunity: ${opportunity.symbolA}/${opportunity.symbolB} - ${(opportunity.netProfit * 100).toFixed(2)}% profit`);
    };

    const handleTradeExecuted = (trade: TradeExecution) => {
      if (trade.status === 'success') {
        toast.success(`Trade executed successfully! Profit: ${trade.actualProfit?.toFixed(6)} ETH`);
      } else if (trade.status === 'failed') {
        toast.error(`Trade failed: ${trade.errorMessage}`);
      }
      loadTradeStats(); // Refresh trade stats
    };

    const handleExecutionError = ({ message }: { message: string }) => {
      toast.error(`Execution error: ${message}`);
    };

    const handleExecutionStarted = ({ opportunityId, amountIn }: { opportunityId: number, amountIn: number }) => {
      setWsActivity(true);
      toast.loading(`Executing arbitrage for opportunity ${opportunityId} with ${amountIn} ETH...`, { id: 'execute' });
    };

    const handleExecutionCompleted = (trade: TradeExecution) => {
      setWsActivity(false);
      if (trade.status === 'success') {
        toast.success(`Trade executed successfully! Profit: ${trade.actualProfit?.toFixed(6)} ETH`, { id: 'execute' });
      } else {
        toast.error(`Trade execution completed with status: ${trade.status}`, { id: 'execute' });
      }
      // Refresh data
      loadInitialData();
    };

    socket.on('opportunities_update', handleOpportunitiesUpdate);
    socket.on('trades_update', handleTradesUpdate);
    socket.on('new_opportunity', handleNewOpportunity);
    socket.on('trade_executed', handleTradeExecuted);
    socket.on('execution_error', handleExecutionError);
    socket.on('execution_started', handleExecutionStarted);
    socket.on('execution_completed', handleExecutionCompleted);

    return () => {
      socket.off('opportunities_update', handleOpportunitiesUpdate);
      socket.off('trades_update', handleTradesUpdate);
      socket.off('new_opportunity', handleNewOpportunity);
      socket.off('trade_executed', handleTradeExecuted);
      socket.off('execution_error', handleExecutionError);
      socket.off('execution_started', handleExecutionStarted);
      socket.off('execution_completed', handleExecutionCompleted);
    };
  }, [socket]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [opportunitiesData, tradesData, systemData] = await Promise.all([
        opportunitiesApi.getAll(),
        tradesApi.getAll(20),
        systemApi.getInfo(),
      ]);

      console.log('REST API: Loaded', opportunitiesData.length, 'opportunities');
      setOpportunities(opportunitiesData);
      setTrades(tradesData);
      setSystemInfo(systemData);
      setInitialDataLoaded(true);

      // Load stats
      await Promise.all([
        loadOpportunityStats(),
        loadTradeStats(),
      ]);

    } catch (err: any) {
      console.error('Error loading initial data:', err);
      setError(err.message || 'Failed to load data');
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const loadOpportunityStats = async () => {
    try {
      const stats = await opportunitiesApi.getStats();
      setOpportunityStats(stats);
    } catch (err) {
      console.error('Error loading opportunity stats:', err);
    }
  };

  const loadTradeStats = async () => {
    try {
      const stats = await tradesApi.getStats();
      setTradeStats(stats);
    } catch (err) {
      console.error('Error loading trade stats:', err);
    }
  };

  const handleExecuteArbitrage = async (opportunityId: number, amountIn: number) => {
    try {
      console.log(`Executing arbitrage for opportunity ${opportunityId} with amount ${amountIn}`);

      // Validate before execution
      const validation = validateArbitrageExecution(amountIn);

      if (!validation.canExecute) {
        toast.error(validation.error || 'Cannot execute arbitrage');
        return;
      }

      if (validation.error) {
        toast.warning(validation.error);
      }

      // Use WebSocket for real-time updates
      socket?.executeArbitrage(opportunityId, amountIn);

      // The execution_started event will show the loading toast
    } catch (err: any) {
      console.error('Error executing arbitrage:', err);
      toast.error(err.message || 'Failed to execute arbitrage');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner w-12 h-12 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-danger-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Dashboard</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button onClick={loadInitialData} className="btn-primary">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Zap className="w-8 h-8 text-primary-600 mr-3" />
              <h1 className="text-xl font-bold text-gradient">Flash Loan Arbitrage</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Contract Error Indicator */}
              {contractError && (
                <div className="flex items-center space-x-2 bg-red-50 px-3 py-1 rounded-lg">
                  <AlertTriangle className="w-4 h-4 text-red-600" />
                  <span className="text-sm text-red-700 font-medium">Contract Error</span>
                </div>
              )}

              {/* Wallet Status */}
              {!walletState.isConnected && (
                <div className="flex items-center space-x-2 bg-yellow-50 px-3 py-1 rounded-lg">
                  <Wallet className="w-4 h-4 text-yellow-600" />
                  <span className="text-sm text-yellow-700 font-medium">Wallet Disconnected</span>
                </div>
              )}

              {/* WebSocket Activity Indicator */}
              {wsActivity && (
                <div className="flex items-center space-x-2">
                  <div className="loading-spinner w-4 h-4"></div>
                  <span className="text-sm text-primary-600 font-medium">Syncing...</span>
                </div>
              )}

              {/* Connection Status */}
              <div className="flex items-center space-x-2">
                <div className={`pulse-dot ${isConnected ? 'bg-success-500' : 'bg-danger-500'}`}></div>
                <span className={`text-sm ${isConnected ? 'status-online' : 'status-offline'}`}>
                  {isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              
              {/* Wallet Connection */}
              <WalletConnection />
              
              {/* Settings */}
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Settings className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Stats Overview */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Active Opportunities */}
          <div className="card">
            <div className="flex items-center">
              <div className="p-2 bg-primary-100 rounded-lg">
                <Activity className="w-6 h-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Opportunities</p>
                <p className="text-2xl font-bold text-gray-900">{opportunityStats?.total || 0}</p>
              </div>
            </div>
          </div>

          {/* Total Profit Potential */}
          <div className="card">
            <div className="flex items-center">
              <div className="p-2 bg-success-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-success-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Potential Profit</p>
                <p className="text-2xl font-bold text-gray-900">
                  {(opportunityStats?.totalNetProfit || 0).toFixed(4)} ETH
                </p>
              </div>
            </div>
          </div>

          {/* Success Rate */}
          <div className="card">
            <div className="flex items-center">
              <div className="p-2 bg-warning-100 rounded-lg">
                <Zap className="w-6 h-6 text-warning-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {(tradeStats?.successRate || 0).toFixed(1)}%
                </p>
              </div>
            </div>
          </div>

          {/* Total Trades */}
          <div className="card">
            <div className="flex items-center">
              <div className="p-2 bg-primary-100 rounded-lg">
                <Wallet className="w-6 h-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Trades</p>
                <p className="text-2xl font-bold text-gray-900">{tradeStats?.total || 0}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'opportunities', label: 'Opportunities', icon: Activity },
              { id: 'trades', label: 'Trades', icon: TrendingUp },
              { id: 'prices', label: 'Prices', icon: Zap },
              { id: 'system', label: 'System', icon: Settings },
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as any)}
                className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === 'opportunities' && (
            <OpportunityList
              opportunities={opportunities}
              onExecute={handleExecuteArbitrage}
              stats={opportunityStats}
            />
          )}
          
          {activeTab === 'trades' && (
            <TradeHistory
              trades={trades}
              stats={tradeStats}
            />
          )}
          
          {activeTab === 'prices' && (
            <PriceMonitor />
          )}
          
          {activeTab === 'system' && (
            <SystemStatus
              systemInfo={systemInfo}
              connectionError={connectionError}
            />
          )}
        </div>
      </div>
    </div>
  );
};
