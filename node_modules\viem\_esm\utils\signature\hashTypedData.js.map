{"version": 3, "file": "hashTypedData.js", "sourceRoot": "", "sources": ["../../../utils/signature/hashTypedData.ts"], "names": [], "mappings": "AAAA,mHAAmH;AAOnH,OAAO,EAEL,mBAAmB,GACpB,MAAM,+BAA+B,CAAA;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAA;AAC1C,OAAO,EAAuB,KAAK,EAAE,MAAM,sBAAsB,CAAA;AACjE,OAAO,EAA2B,SAAS,EAAE,MAAM,sBAAsB,CAAA;AACzE,OAAO,EAGL,uBAAuB,EACvB,iBAAiB,GAClB,MAAM,iBAAiB,CAAA;AAqBxB,MAAM,UAAU,aAAa,CAG3B,EACA,MAAM,EAAE,OAAO,EACf,OAAO,EACP,WAAW,EACX,KAAK,EAAE,MAAM,GACqC;IAClD,MAAM,MAAM,GAAoB,OAAO,OAAO,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAA;IAC7E,MAAM,KAAK,GAAG;QACZ,YAAY,EAAE,uBAAuB,CAAC,EAAE,MAAM,EAAE,CAAC;QACjD,GAAI,MAAqB;KAC1B,CAAA;IAED,uFAAuF;IACvF,qDAAqD;IACrD,iBAAiB,CAAC;QAChB,MAAM;QACN,OAAO;QACP,WAAW;QACX,KAAK;KACiB,CAAC,CAAA;IAEzB,MAAM,KAAK,GAAU,CAAC,QAAQ,CAAC,CAAA;IAC/B,IAAI,MAAM;QACR,KAAK,CAAC,IAAI,CACR,UAAU,CAAC;YACT,MAAM;YACN,KAAK,EAAE,KAA8C;SACtD,CAAC,CACH,CAAA;IAEH,IAAI,WAAW,KAAK,cAAc,EAAE;QAClC,KAAK,CAAC,IAAI,CACR,UAAU,CAAC;YACT,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,WAAqB;YAClC,KAAK,EAAE,KAA8C;SACtD,CAAC,CACH,CAAA;KACF;IAED,OAAO,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;AACjC,CAAC;AAID,MAAM,UAAU,UAAU,CAAC,EACzB,MAAM,EACN,KAAK,GAIN;IACC,OAAO,UAAU,CAAC;QAChB,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,cAAc;QAC3B,KAAK;KACN,CAAC,CAAA;AACJ,CAAC;AAID,SAAS,UAAU,CAAC,EAClB,IAAI,EACJ,WAAW,EACX,KAAK,GAKN;IACC,MAAM,OAAO,GAAG,UAAU,CAAC;QACzB,IAAI;QACJ,WAAW;QACX,KAAK;KACN,CAAC,CAAA;IACF,OAAO,SAAS,CAAC,OAAO,CAAC,CAAA;AAC3B,CAAC;AAQD,SAAS,UAAU,CAAC,EAClB,IAAI,EACJ,WAAW,EACX,KAAK,GAKN;IACC,MAAM,YAAY,GAAmB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAA;IAC1D,MAAM,aAAa,GAAc,CAAC,QAAQ,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC,CAAA;IAEnE,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE;QACtC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,WAAW,CAAC;YAChC,KAAK;YACL,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;SACxB,CAAC,CAAA;QACF,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACvB,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;KAC1B;IAED,OAAO,mBAAmB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAA;AACzD,CAAC;AAQD,SAAS,QAAQ,CAAC,EAChB,WAAW,EACX,KAAK,GAIN;IACC,MAAM,eAAe,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC,CAAA;IACjE,OAAO,SAAS,CAAC,eAAe,CAAC,CAAA;AACnC,CAAC;AAID,SAAS,UAAU,CAAC,EAClB,WAAW,EACX,KAAK,GAIN;IACC,IAAI,MAAM,GAAG,EAAE,CAAA;IACf,MAAM,YAAY,GAAG,oBAAoB,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAA;IACjE,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;IAEhC,MAAM,IAAI,GAAG,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;IAC9D,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;QACvB,MAAM,IAAI,GAAG,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC;aAC7B,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;aAC1C,IAAI,CAAC,GAAG,CAAC,GAAG,CAAA;KAChB;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAID,SAAS,oBAAoB,CAC3B,EACE,WAAW,EAAE,YAAY,EACzB,KAAK,GAIN,EACD,UAAuB,IAAI,GAAG,EAAE;IAEhC,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;IACzC,MAAM,WAAW,GAAG,KAAK,EAAE,CAAC,CAAC,CAAE,CAAA;IAC/B,IAAI,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,SAAS,EAAE;QAChE,OAAO,OAAO,CAAA;KACf;IAED,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;IAExB,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE;QACtC,oBAAoB,CAAC,EAAE,WAAW,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,OAAO,CAAC,CAAA;KAClE;IACD,OAAO,OAAO,CAAA;AAChB,CAAC;AAQD,SAAS,WAAW,CAAC,EACnB,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,KAAK,GAMN;IACC,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;QAC7B,OAAO;YACL,EAAE,IAAI,EAAE,SAAS,EAAE;YACnB,SAAS,CAAC,UAAU,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;SACjE,CAAA;KACF;IAED,IAAI,IAAI,KAAK,OAAO,EAAE;QACpB,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAA;QAC3C,KAAK,GAAG,KAAK,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAA;QACvC,OAAO,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAA;KAC/C;IAED,IAAI,IAAI,KAAK,QAAQ;QAAE,OAAO,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IAE5E,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAA;QACvD,MAAM,cAAc,GAAI,KAA+B,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CACnE,WAAW,CAAC;YACV,IAAI;YACJ,IAAI,EAAE,UAAU;YAChB,KAAK;YACL,KAAK,EAAE,IAAI;SACZ,CAAC,CACH,CAAA;QACD,OAAO;YACL,EAAE,IAAI,EAAE,SAAS,EAAE;YACnB,SAAS,CACP,mBAAmB,CACjB,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAC9B,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CACjC,CACF;SACF,CAAA;KACF;IAED,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,CAAC,CAAA;AAC1B,CAAC"}