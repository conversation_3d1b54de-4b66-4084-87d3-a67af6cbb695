import React, { useState } from 'react';
import { Wallet, ChevronDown, Copy, ExternalLink, LogOut } from 'lucide-react';
import { useWeb3 } from '@/contexts/Web3Context';

export const WalletConnection: React.FC = () => {
  const { walletState, connectWallet, disconnectWallet, contractError } = useWeb3();
  const [showDropdown, setShowDropdown] = useState(false);

  const handleConnect = async () => {
    try {
      await connectWallet();
    } catch (error: any) {
      alert('Failed to connect wallet: ' + error.message);
    }
  };

  const handleDisconnect = () => {
    disconnectWallet();
    setShowDropdown(false);
  };

  const copyAddress = () => {
    if (walletState.address) {
      navigator.clipboard.writeText(walletState.address);
      alert('Address copied to clipboard');
    }
  };

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const getEtherscanUrl = (addr: string) => {
    return `https://etherscan.io/address/${addr}`;
  };

  if (!walletState.isConnected) {
    return (
      <div>
        <button
          onClick={handleConnect}
          disabled={walletState.isConnecting}
          className="btn-primary flex items-center"
        >
          <Wallet className="w-4 h-4 mr-2" />
          {walletState.isConnecting ? 'Connecting...' : 'Connect Wallet'}
        </button>
        {walletState.error && (
          <p className="text-red-600 text-xs mt-1">{walletState.error}</p>
        )}
        {contractError && (
          <p className="text-orange-600 text-xs mt-1">{contractError}</p>
        )}
      </div>
    );
  }

  return (
    <div className="relative">
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        className="flex items-center space-x-2 bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm hover:bg-gray-50 transition-colors"
      >
        <div className="w-6 h-6 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
          <Wallet className="w-3 h-3 text-white" />
        </div>
        <div className="text-left">
          <p className="font-medium text-gray-900">{formatAddress(walletState.address || '')}</p>
          <p className="text-xs text-gray-500">{walletState.balance || '0.0'} ETH</p>
        </div>
        <ChevronDown className="w-4 h-4 text-gray-400" />
      </button>

      {showDropdown && (
        <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                <Wallet className="w-5 h-5 text-white" />
              </div>
              <div>
                <p className="font-medium text-gray-900">Connected</p>
                <p className="text-sm text-gray-500">{formatAddress(address)}</p>
              </div>
            </div>
          </div>

          <div className="p-2">
            <div className="px-3 py-2 text-sm">
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-600">Balance:</span>
                <span className="font-medium">{walletState.balance || '0.0'} ETH</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Network:</span>
                <span className="font-medium">Ethereum</span>
              </div>
            </div>

            <div className="border-t border-gray-200 mt-2 pt-2">
              <button
                onClick={copyAddress}
                className="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Copy className="w-4 h-4 mr-3" />
                Copy Address
              </button>

              <a
                href={getEtherscanUrl(walletState.address || '')}
                target="_blank"
                rel="noopener noreferrer"
                className="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ExternalLink className="w-4 h-4 mr-3" />
                View on Etherscan
              </a>

              <button
                onClick={handleDisconnect}
                className="w-full flex items-center px-3 py-2 text-sm text-danger-600 hover:bg-danger-50 rounded-lg transition-colors"
              >
                <LogOut className="w-4 h-4 mr-3" />
                Disconnect
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Click outside to close dropdown */}
      {showDropdown && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  );
};


