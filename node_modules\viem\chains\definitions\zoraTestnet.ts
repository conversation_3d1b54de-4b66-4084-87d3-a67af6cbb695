import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'
import { formattersOptimism } from '../optimism/formatters.js'

export const zoraTestnet = /*#__PURE__*/ define<PERSON>hain(
  {
    id: 999,
    name: '<PERSON>ora Go<PERSON>li Testnet',
    network: 'zora-testnet',
    nativeCurrency: {
      decimals: 18,
      name: '<PERSON><PERSON> Go<PERSON>li',
      symbol: 'ETH',
    },
    rpcUrls: {
      default: {
        http: ['https://testnet.rpc.zora.energy'],
        webSocket: ['wss://testnet.rpc.zora.energy'],
      },
      public: {
        http: ['https://testnet.rpc.zora.energy'],
        webSocket: ['wss://testnet.rpc.zora.energy'],
      },
    },
    blockExplorers: {
      default: {
        name: 'Explorer',
        url: 'https://testnet.explorer.zora.energy',
      },
    },
    contracts: {
      multicall3: {
        address: '******************************************',
        blockCreated: 189123,
      },
    },
    testnet: true,
  },
  {
    formatters: formattersOptimism,
  },
)
