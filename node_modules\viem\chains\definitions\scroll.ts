import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const scroll = /*#__PURE__*/ defineChain({
  id: 534_352,
  name: '<PERSON><PERSON>',
  network: 'scroll',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.scroll.io'],
      webSocket: ['wss://wss-rpc.scroll.io/ws'],
    },
    public: {
      http: ['https://rpc.scroll.io'],
      webSocket: ['wss://wss-rpc.scroll.io/ws'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Scrolls<PERSON>',
      url: 'https://scrollscan.com',
    },
    blockscout: {
      name: 'Blockscout',
      url: 'https://blockscout.scroll.io',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 14,
    },
  },
  testnet: false,
})
