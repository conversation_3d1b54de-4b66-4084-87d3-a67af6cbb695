{"name": "@adraffy/ens-normalize", "version": "1.10.0", "description": "Ethereum Name Service (ENS) Name Normalizer", "keywords": ["ENS", "ENSIP-1", "ENSIP-15", "Ethereum", "UTS-46", "UTS-51", "IDNA", "Name", "Normalize", "Normalization", "NFC", "NFD"], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "default": "./dist/index.cjs"}, "./xnf": {"types": "./dist/index.d.ts", "import": "./dist/index-xnf.mjs", "default": "./dist/index-xnf.cjs"}}, "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/index.d.ts"]}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "files": ["./dist"], "repository": {"type": "git", "url": "git+https://github.com/adraffy/ens-normalize.js.git"}, "license": "MIT", "bugs": {"url": "https://github.com/adraffy/ens-normalize.js/issues"}, "homepage": "https://github.com/adraffy/ens-normalize.js#readme", "author": {"name": "raffy.eth", "email": "<EMAIL>", "url": "http://raffy.antistupid.com"}, "scripts": {"unicode": "node derive/download.js", "labels": "node validate/download-labels.js", "derive": "node derive/make.js", "make": "node src/make.js", "validate": "node validate/make.js", "test": "node test/coder.js && node test/nf.js && node test/validate.js && node test/init.js", "build": "rollup -c", "rebuild": "npm run derive && npm run make && npm run validate && npm run test && npm run build", "order": "node validate/dump-group-order.js save && npm run rebuild", "pack": "node ./src/prepost.js pack", "pub": "node ./src/prepost.js publish"}, "devDependencies": {"@rollup/plugin-alias": "^5.0.0", "@rollup/plugin-terser": "^0.4.0", "rollup": "^3.24.1"}}