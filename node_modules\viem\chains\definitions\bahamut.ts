import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const bahamut = /*#__PURE__*/ define<PERSON>hain({
  id: 5165,
  network: 'bahamut',
  name: '<PERSON><PERSON><PERSON>',
  nativeCurrency: { name: '<PERSON><PERSON><PERSON>', symbol: 'FTN', decimals: 18 },
  rpcUrls: {
    default: {
      http: [
        'https://rpc1.bahamut.io',
        'https://bahamut.publicnode.com',
        'https://rpc2.bahamut.io',
      ],
      webSocket: [
        'wss://ws1.sahara.bahamutchain.com',
        'wss://bahamut.publicnode.com',
        'wss://ws2.sahara.bahamutchain.com',
      ],
    },
    public: {
      http: [
        'https://rpc1.bahamut.io',
        'https://bahamut.publicnode.com',
        'https://rpc2.bahamut.io',
      ],
      webSocket: [
        'wss://ws1.sahara.bahamutchain.com',
        'wss://bahamut.publicnode.com',
        'wss://ws2.sahara.bahamutchain.com',
      ],
    },
  },
  blockExplorers: {
    default: {
      name: 'Ftnscan',
      url: 'https://www.ftnscan.com',
    },
  },
})
