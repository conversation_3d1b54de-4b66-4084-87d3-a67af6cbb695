import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const metisGoerli = /*#__PURE__*/ define<PERSON>hain({
  id: 599,
  name: '<PERSON><PERSON> Go<PERSON>li',
  network: 'metis-goerli',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: 'METIS',
  },
  rpcUrls: {
    default: { http: ['https://goerli.gateway.metisdevops.link'] },
    public: { http: ['https://goerli.gateway.metisdevops.link'] },
  },
  blockExplorers: {
    default: {
      name: 'Metis Goerli Explorer',
      url: 'https://goerli.explorer.metisdevops.link',
    },
  },
  contracts: {
    multicall3: {
      address: '0xca11bde05977b3631167028862be2a173976ca11',
      blockCreated: 1006207,
    },
  },
})
