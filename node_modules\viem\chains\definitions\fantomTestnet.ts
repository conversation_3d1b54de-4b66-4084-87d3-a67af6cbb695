import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const fantomTestnet = /*#__PURE__*/ define<PERSON>hain({
  id: 4_002,
  name: 'Fantom Testnet',
  network: 'fantom-testnet',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: 'FTM',
  },
  rpcUrls: {
    default: { http: ['https://rpc.testnet.fantom.network'] },
    public: { http: ['https://rpc.testnet.fantom.network'] },
  },
  blockExplorers: {
    etherscan: { name: 'FTMScan', url: 'https://testnet.ftmscan.com' },
    default: { name: 'FTMScan', url: 'https://testnet.ftmscan.com' },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 8328688,
    },
  },
})
