import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const eos = /*#__PURE__*/ defineChain({
  id: 17777,
  name: 'EOS EVM',
  network: 'eos',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: '<PERSON><PERSON>',
  },
  rpcUrls: {
    default: { http: ['https://api.evm.eosnetwork.com'] },
    public: { http: ['https://api.evm.eosnetwork.com'] },
  },
  blockExplorers: {
    etherscan: {
      name: 'EOS EVM Explorer',
      url: 'https://explorer.evm.eosnetwork.com',
    },
    default: {
      name: 'EOS EVM Explorer',
      url: 'https://explorer.evm.eosnetwork.com',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 7943933,
    },
  },
})
