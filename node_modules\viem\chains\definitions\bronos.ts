import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const bronos = /*#__PURE__*/ define<PERSON>hain({
  id: 1039,
  name: 'Bronos',
  network: 'bronos',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: '<PERSON><PERSON>',
  },
  rpcUrls: {
    default: { http: ['https://evm.bronos.org'] },
    public: { http: ['https://evm.bronos.org'] },
  },
  blockExplorers: {
    default: { name: 'BronoScan', url: 'https://broscan.bronos.org' },
  },
})
