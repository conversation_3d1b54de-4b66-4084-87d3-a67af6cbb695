{"version": 3, "file": "observe.js", "sourceRoot": "", "sources": ["../../utils/observe.ts"], "names": [], "mappings": "AAQA,MAAM,CAAC,MAAM,cAAc,GAAG,aAAa,CAAC,IAAI,GAAG,EAGhD,CAAA;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,aAAa,CAAC,IAAI,GAAG,EAAsB,CAAA;AAMvE,IAAI,aAAa,GAAG,CAAC,CAAA;AAErB;;;;GAIG;AACH,MAAM,UAAU,OAAO,CACrB,UAAkB,EAClB,SAAqB,EACrB,EAA4B;IAE5B,MAAM,UAAU,GAAG,EAAE,aAAa,CAAA;IAElC,MAAM,YAAY,GAAG,GAAG,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAA;IAE/D,MAAM,WAAW,GAAG,GAAG,EAAE;QACvB,MAAM,SAAS,GAAG,YAAY,EAAE,CAAA;QAChC,cAAc,CAAC,GAAG,CAChB,UAAU,EACV,SAAS,CAAC,MAAM,CAAC,CAAC,EAAO,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,UAAU,CAAC,CACpD,CAAA;IACH,CAAC,CAAA;IAED,MAAM,OAAO,GAAG,GAAG,EAAE;QACnB,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;QAC5C,IAAI,YAAY,EAAE,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO;YAAE,OAAO,EAAE,CAAA;QACrD,WAAW,EAAE,CAAA;IACf,CAAC,CAAA;IAED,MAAM,SAAS,GAAG,YAAY,EAAE,CAAA;IAChC,cAAc,CAAC,GAAG,CAAC,UAAU,EAAE;QAC7B,GAAG,SAAS;QACZ,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE,SAAS,EAAE;KACnC,CAAC,CAAA;IAEF,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC;QAAE,OAAO,OAAO,CAAA;IAErD,MAAM,IAAI,GAAe,EAAgB,CAAA;IACzC,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE;QAC3B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CACX,GAAG,IAA2D,EAC9D,EAAE;YACF,MAAM,SAAS,GAAG,YAAY,EAAE,CAAA;YAChC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAM;YAClC,KAAK,MAAM,QAAQ,IAAI,SAAS;gBAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAA;QAChE,CAAC,CAAkD,CAAA;KACpD;IAED,MAAM,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,CAAA;IACxB,IAAI,OAAO,OAAO,KAAK,UAAU;QAAE,YAAY,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;IAExE,OAAO,OAAO,CAAA;AAChB,CAAC"}