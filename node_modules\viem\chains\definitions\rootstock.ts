import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const rootstock = /*#__PURE__*/ defineChain({
  id: 30,
  name: 'Rootstock Mainnet',
  network: 'rootstock',
  nativeCurrency: {
    decimals: 18,
    name: 'Rootstock Bitcoin',
    symbol: 'RBTC',
  },
  rpcUrls: {
    public: { http: ['https://public-node.rsk.co'] },
    default: { http: ['https://public-node.rsk.co'] },
  },
  blockExplorers: {
    blockscout: { name: 'Blockscout', url: 'https://rootstock.blockscout.com' },
    default: { name: 'RSK Explorer', url: 'https://explorer.rsk.co' },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 4249540,
    },
  },
})
