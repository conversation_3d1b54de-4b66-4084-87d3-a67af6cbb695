import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const eosTestnet = /*#__PURE__*/ define<PERSON>hain({
  id: 15557,
  name: 'EOS EVM Testnet',
  network: 'eos',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: '<PERSON><PERSON>',
  },
  rpcUrls: {
    default: { http: ['https://api.testnet.evm.eosnetwork.com'] },
    public: { http: ['https://api.testnet.evm.eosnetwork.com'] },
  },
  blockExplorers: {
    etherscan: {
      name: 'EOS EVM Testnet Explorer',
      url: 'https://explorer.testnet.evm.eosnetwork.com',
    },
    default: {
      name: 'EOS EVM Testnet Explorer',
      url: 'https://explorer.testnet.evm.eosnetwork.com',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 9067940,
    },
  },
  testnet: true,
})
