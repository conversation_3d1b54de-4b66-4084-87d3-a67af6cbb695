{"version": 3, "file": "block.js", "sourceRoot": "", "sources": ["../../../utils/formatters/block.ts"], "names": [], "mappings": "AAWA,OAAO,EAAiC,eAAe,EAAE,MAAM,gBAAgB,CAAA;AAC/E,OAAO,EAA6B,iBAAiB,EAAE,MAAM,kBAAkB,CAAA;AAgC/E,MAAM,UAAU,WAAW,CAAC,KAAwB;IAClD,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;QAC3D,IAAI,OAAO,WAAW,KAAK,QAAQ;YAAE,OAAO,WAAW,CAAA;QACvD,OAAO,iBAAiB,CAAC,WAAW,CAAC,CAAA;IACvC,CAAC,CAAC,CAAA;IACF,OAAO;QACL,GAAG,KAAK;QACR,aAAa,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI;QACvE,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS;QACnE,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS;QAC7D,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;QAC1D,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;QACpC,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI;QACnD,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;QACvC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI;QAClD,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;QACjD,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS;QAChE,YAAY;QACZ,eAAe,EAAE,KAAK,CAAC,eAAe;YACpC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC;YAC/B,CAAC,CAAC,IAAI;KACA,CAAA;AACZ,CAAC;AAID,MAAM,CAAC,MAAM,WAAW,GAAG,aAAa,CAAC,eAAe,CAAC,OAAO,EAAE,WAAW,CAAC,CAAA"}