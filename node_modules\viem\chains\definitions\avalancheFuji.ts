import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const avalancheFuji = /*#__PURE__*/ define<PERSON>hain({
  id: 43_113,
  name: 'Avalanche Fuji',
  network: 'avalanche-fuji',
  nativeCurrency: {
    decimals: 18,
    name: 'Avalanche Fuji',
    symbol: 'AVAX',
  },
  rpcUrls: {
    default: { http: ['https://api.avax-test.network/ext/bc/C/rpc'] },
    public: { http: ['https://api.avax-test.network/ext/bc/C/rpc'] },
  },
  blockExplorers: {
    etherscan: { name: 'SnowTrace', url: 'https://testnet.snowtrace.io' },
    default: { name: 'SnowTrace', url: 'https://testnet.snowtrace.io' },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 7096959,
    },
  },
  testnet: true,
})
