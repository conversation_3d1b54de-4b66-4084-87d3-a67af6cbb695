import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'
import { formattersOptimism } from '../optimism/formatters.js'

export const optimismSepolia = /*#__PURE__*/ define<PERSON>hain(
  {
    id: 11155420,
    name: 'Optimism Sepolia',
    network: 'optimism-sepolia',
    nativeCurrency: { name: 'Sepolia Ether', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
      alchemy: {
        http: ['https://opt-sepolia.g.alchemy.com/v2'],
        webSocket: ['wss://opt-sepolia.g.alchemy.com/v2'],
      },
      default: {
        http: ['https://sepolia.optimism.io'],
      },
      public: {
        http: ['https://sepolia.optimism.io'],
      },
    },
    blockExplorers: {
      blockscout: {
        name: 'Blockscout',
        url: 'https://optimism-sepolia.blockscout.com',
      },
      default: {
        name: 'Blockscout',
        url: 'https://optimism-sepolia.blockscout.com',
      },
    },
    contracts: {
      multicall3: {
        address: '******************************************',
        blockCreated: 1620204,
      },
    },
    testnet: true,
  },
  {
    formatters: formattersOptimism,
  },
)
