import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const scrollTestnet = /*#__PURE__*/ defineChain({
  id: 534_353,
  name: 'Scroll Testnet',
  network: 'scroll-testnet',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://alpha-rpc.scroll.io/l2'],
      webSocket: ['wss://alpha-rpc.scroll.io/l2/ws'],
    },
    public: {
      http: ['https://alpha-rpc.scroll.io/l2'],
      webSocket: ['wss://alpha-rpc.scroll.io/l2/ws'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Blockscout',
      url: 'https://blockscout.scroll.io',
    },
  },
  testnet: true,
})
