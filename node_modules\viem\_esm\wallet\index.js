// TODO(v2): Remove this entrypoint. Favor importing from actions entrypoint (`viem/actions`).
export { addChain, } from '../actions/wallet/addChain.js';
export { getAddresses, } from '../actions/wallet/getAddresses.js';
export { getPermissions, } from '../actions/wallet/getPermissions.js';
export { requestAddresses, } from '../actions/wallet/requestAddresses.js';
export { requestPermissions, } from '../actions/wallet/requestPermissions.js';
export { sendTransaction, } from '../actions/wallet/sendTransaction.js';
export { signMessage, } from '../actions/wallet/signMessage.js';
export { signTypedData, } from '../actions/wallet/signTypedData.js';
export { switchChain, } from '../actions/wallet/switchChain.js';
export { watchAsset, } from '../actions/wallet/watchAsset.js';
//# sourceMappingURL=index.js.map