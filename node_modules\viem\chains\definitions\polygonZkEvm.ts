import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const polygonZkEvm = /*#__PURE__*/ define<PERSON>hain({
  id: 1101,
  name: 'Polygon zkEVM',
  network: 'polygon-zkevm',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://zkevm-rpc.com'],
    },
    public: {
      http: ['https://zkevm-rpc.com'],
    },
  },
  blockExplorers: {
    default: {
      name: 'PolygonScan',
      url: 'https://zkevm.polygonscan.com',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 57746,
    },
  },
})
