{"version": 3, "file": "hashMessage.js", "sourceRoot": "", "sources": ["../../../utils/signature/hashMessage.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,4BAA4B,CAAA;AAGjE,OAAO,EAAwB,MAAM,EAAE,MAAM,mBAAmB,CAAA;AAChE,OAAO,EAGL,aAAa,EACb,OAAO,GACR,MAAM,wBAAwB,CAAA;AAC/B,OAAO,EAA2B,SAAS,EAAE,MAAM,sBAAsB,CAAA;AAezE,MAAM,UAAU,WAAW,CACzB,OAAwB,EACxB,GAAS;IAET,MAAM,YAAY,GAAG,CAAC,GAAG,EAAE;QACzB,IAAI,OAAO,OAAO,KAAK,QAAQ;YAAE,OAAO,aAAa,CAAC,OAAO,CAAC,CAAA;QAC9D,IAAI,OAAO,CAAC,GAAG,YAAY,UAAU;YAAE,OAAO,OAAO,CAAC,GAAG,CAAA;QACzD,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;IAC7B,CAAC,CAAC,EAAE,CAAA;IACJ,MAAM,WAAW,GAAG,aAAa,CAC/B,GAAG,oBAAoB,GAAG,YAAY,CAAC,MAAM,EAAE,CAChD,CAAA;IACD,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;AAC5D,CAAC"}