import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const zilliqa = /*#__PURE__*/ defineChain({
  id: 32769,
  name: '<PERSON><PERSON>iq<PERSON>',
  network: 'zilliqa',
  nativeCurrency: { name: '<PERSON><PERSON><PERSON><PERSON>', symbol: 'ZIL', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://api.zilliqa.com'],
    },
    public: {
      http: ['https://api.zilliqa.com'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Ethernal',
      url: 'https://evmx.zilliqa.com',
    },
  },
  testnet: false,
})
