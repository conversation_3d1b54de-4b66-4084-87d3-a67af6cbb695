import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const boba = /*#__PURE__*/ defineChain({
  id: 288,
  name: 'Boba Network',
  network: 'boba',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: 'BO<PERSON>',
  },
  rpcUrls: {
    default: { http: ['https://mainnet.boba.network'] },
    public: { http: ['https://mainnet.boba.network'] },
  },
  blockExplorers: {
    etherscan: { name: 'BOBAS<PERSON>', url: 'https://bobascan.com' },
    default: { name: 'BOBAS<PERSON>', url: 'https://bobascan.com' },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 446859,
    },
  },
})
