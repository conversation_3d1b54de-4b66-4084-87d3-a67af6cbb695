{"version": 3, "file": "fromRlp.js", "sourceRoot": "", "sources": ["../../../utils/encoding/fromRlp.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAsB,MAAM,sBAAsB,CAAA;AACpE,OAAO,EACL,oBAAoB,GAErB,MAAM,0BAA0B,CAAA;AAGjC,OAAO,EAGL,YAAY,GACb,MAAM,cAAc,CAAA;AACrB,OAAO,EAA4B,UAAU,EAAE,MAAM,cAAc,CAAA;AACnE,OAAO,EAA4B,UAAU,EAAE,MAAM,YAAY,CAAA;AAiBjE,MAAM,UAAU,OAAO,CACrB,KAAsB,EACtB,KAA0B,KAAK;IAE/B,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE;QAClB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC;gBAC5C,MAAM,IAAI,oBAAoB,CAAC,KAAK,CAAC,CAAA;YACvC,OAAO,UAAU,CAAC,KAAK,CAAC,CAAA;SACzB;QACD,OAAO,KAAK,CAAA;IACd,CAAC,CAAC,EAAE,CAAA;IAEJ,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,CAAA;IAClC,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;IAExC,OAAO,MAA+B,CAAA;AACxC,CAAC;AAID,MAAM,UAAU,UAAU,CACxB,KAAgB,EAChB,KAA0B,OAAO;IAEjC,OAAO,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;AAC3B,CAAC;AAID,MAAM,UAAU,QAAQ,CACtB,GAAQ,EACR,KAA0B,KAAK;IAE/B,OAAO,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;AACzB,CAAC;AAQD,SAAS,aAAa,CACpB,MAAc,EACd,KAA0B,KAAK;IAE/B,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC;QAC3B,OAAO,CACL,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAC9B,CAAA;IAE5B,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAA;IAChC,IAAI,MAAM,GAAG,IAAI;QAAE,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAA;IAE9C,QAAQ;IACR,IAAI,MAAM,GAAG,IAAI,EAAE;QACjB,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;QAC/C,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QACtC,OAAO,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAA0B,CAAA;KAC3E;IAED,OAAO;IACP,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;IAC/C,OAAO,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,CAAgC,CAAA;AACpE,CAAC;AAID,SAAS,UAAU,CAAC,MAAc,EAAE,MAAc,EAAE,MAAc;IAChE,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,GAAG,IAAI;QAAE,OAAO,CAAC,CAAA;IAC9C,IAAI,MAAM,IAAI,MAAM,GAAG,EAAE;QAAE,OAAO,MAAM,GAAG,MAAM,CAAA;IACjD,IAAI,MAAM,KAAK,MAAM,GAAG,EAAE,GAAG,CAAC;QAAE,OAAO,MAAM,CAAC,SAAS,EAAE,CAAA;IACzD,IAAI,MAAM,KAAK,MAAM,GAAG,EAAE,GAAG,CAAC;QAAE,OAAO,MAAM,CAAC,UAAU,EAAE,CAAA;IAC1D,IAAI,MAAM,KAAK,MAAM,GAAG,EAAE,GAAG,CAAC;QAAE,OAAO,MAAM,CAAC,UAAU,EAAE,CAAA;IAC1D,IAAI,MAAM,KAAK,MAAM,GAAG,EAAE,GAAG,CAAC;QAAE,OAAO,MAAM,CAAC,UAAU,EAAE,CAAA;IAC1D,MAAM,IAAI,SAAS,CAAC,oBAAoB,CAAC,CAAA;AAC3C,CAAC;AAID,SAAS,QAAQ,CAAgB,MAAc,EAAE,MAAc,EAAE,EAAW;IAC1E,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAA;IAChC,MAAM,KAAK,GAA4B,EAAE,CAAA;IACzC,OAAO,MAAM,CAAC,QAAQ,GAAG,QAAQ,GAAG,MAAM;QACxC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAA;IACvC,OAAO,KAAK,CAAA;AACd,CAAC"}