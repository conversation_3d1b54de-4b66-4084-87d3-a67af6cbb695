import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Activity, RefreshCw } from 'lucide-react';
import { useWebSocket } from '@/lib/websocket';
import { formatCurrency, formatTime } from '@/lib/api';
import type { TokenPrice } from '@/types';

export const PriceMonitor: React.FC = () => {
  const [prices, setPrices] = useState<TokenPrice[]>([]);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const { socket } = useWebSocket();

  useEffect(() => {
    if (!socket) return;

    const handlePricesUpdate = (data: TokenPrice[]) => {
      setPrices(data);
      setLastUpdate(new Date());
    };

    socket.on('prices_update', handlePricesUpdate);

    // Request initial prices
    socket.requestPrices();

    return () => {
      socket.off('prices_update', handlePricesUpdate);
    };
  }, [socket]);

  const refreshPrices = () => {
    socket?.requestPrices();
  };

  // Group prices by token pair
  const groupedPrices = prices.reduce((acc, price) => {
    const key = price.tokenAddress;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(price);
    return acc;
  }, {} as Record<string, TokenPrice[]>);

  const getTokenSymbol = (tokenAddress: string) => {
    // This would normally come from a token registry
    const tokenMap: Record<string, string> = {
      '******************************************': 'WETH',
      '******************************************': 'USDC',
      '******************************************': 'DAI',
    };
    return tokenMap[tokenAddress] || tokenAddress.slice(0, 8) + '...';
  };

  const getDexDisplayName = (dex: string) => {
    switch (dex) {
      case 'uniswap_v3': return 'Uniswap V3';
      case 'sushiswap': return 'SushiSwap';
      default: return dex;
    }
  };

  const calculatePriceDifference = (prices: TokenPrice[]) => {
    if (prices.length < 2) return null;
    
    const sortedPrices = [...prices].sort((a, b) => a.price - b.price);
    const lowest = sortedPrices[0];
    const highest = sortedPrices[sortedPrices.length - 1];
    
    const difference = (highest.price - lowest.price) / lowest.price;
    
    return {
      difference,
      lowest,
      highest,
    };
  };

  if (prices.length === 0) {
    return (
      <div className="card text-center py-12">
        <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Price Data</h3>
        <p className="text-gray-600 mb-4">
          Waiting for price updates from DEXs...
        </p>
        <button onClick={refreshPrices} className="btn-primary">
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh Prices
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Price Monitor</h2>
          {lastUpdate && (
            <p className="text-sm text-gray-600">
              Last updated: {formatTime(lastUpdate.toISOString())}
            </p>
          )}
        </div>
        
        <button onClick={refreshPrices} className="btn-secondary btn-sm">
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </button>
      </div>

      {/* Price Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Object.entries(groupedPrices).map(([tokenAddress, tokenPrices]) => {
          const priceDiff = calculatePriceDifference(tokenPrices);
          const symbol = getTokenSymbol(tokenAddress);
          
          return (
            <div key={tokenAddress} className="card">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">{symbol}</h3>
                
                {priceDiff && (
                  <div className="flex items-center">
                    {priceDiff.difference > 0.005 ? (
                      <TrendingUp className="w-4 h-4 text-success-500 mr-1" />
                    ) : (
                      <TrendingDown className="w-4 h-4 text-gray-400 mr-1" />
                    )}
                    <span className={`text-sm font-medium ${
                      priceDiff.difference > 0.005 ? 'text-success-600' : 'text-gray-600'
                    }`}>
                      {(priceDiff.difference * 100).toFixed(2)}% spread
                    </span>
                  </div>
                )}
              </div>

              <div className="space-y-3">
                {tokenPrices.map((price, index) => (
                  <div
                    key={`${price.tokenAddress}-${price.dex}-${index}-${price.timestamp}`}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
                        <Activity className="w-4 h-4 text-primary-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          {getDexDisplayName(price.dex)}
                        </p>
                        <p className="text-sm text-gray-500">
                          {formatTime(price.timestamp)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <p className="font-bold text-gray-900">
                        ${formatCurrency(price.price, 4)}
                      </p>
                      {price.volume24h && (
                        <p className="text-sm text-gray-500">
                          Vol: ${formatCurrency(price.volume24h, 0)}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Price Difference Highlight */}
              {priceDiff && priceDiff.difference > 0.005 && (
                <div className="mt-4 p-3 bg-success-50 rounded-lg border border-success-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-success-800">
                        Arbitrage Opportunity
                      </p>
                      <p className="text-xs text-success-600">
                        Buy on {getDexDisplayName(priceDiff.lowest.dex)}, 
                        sell on {getDexDisplayName(priceDiff.highest.dex)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-bold text-success-800">
                        {(priceDiff.difference * 100).toFixed(2)}%
                      </p>
                      <p className="text-xs text-success-600">
                        Potential profit
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* All Prices Table */}
      <div className="card p-0">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">All Prices</h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="table">
            <thead>
              <tr>
                <th>Token</th>
                <th>DEX</th>
                <th>Price</th>
                <th>Volume (24h)</th>
                <th>Liquidity</th>
                <th>Last Update</th>
              </tr>
            </thead>
            <tbody>
              {prices.map((price, index) => (
                <tr key={`${price.tokenAddress}-${price.dex}-${index}-${price.timestamp}`}>
                  <td>
                    <span className="font-medium">
                      {getTokenSymbol(price.tokenAddress)}
                    </span>
                  </td>
                  <td>
                    <span className="badge-primary">
                      {getDexDisplayName(price.dex)}
                    </span>
                  </td>
                  <td>
                    <span className="font-bold">
                      ${formatCurrency(price.price, 4)}
                    </span>
                  </td>
                  <td>
                    {price.volume24h ? (
                      <span>${formatCurrency(price.volume24h, 0)}</span>
                    ) : (
                      <span className="text-gray-500">-</span>
                    )}
                  </td>
                  <td>
                    {price.liquidity ? (
                      <span>${formatCurrency(price.liquidity, 0)}</span>
                    ) : (
                      <span className="text-gray-500">-</span>
                    )}
                  </td>
                  <td>
                    <span className="text-sm text-gray-600">
                      {formatTime(price.timestamp)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};
