import { define<PERSON>hain } from '../../utils/chain/defineChain.js'
import { formattersCelo } from '../celo/formatters.js'
import { serializersCelo } from '../celo/serializers.js'

export const celo = /*#__PURE__*/ define<PERSON>hain(
  {
    id: 42_220,
    name: '<PERSON><PERSON>',
    network: 'celo',
    nativeCurrency: {
      decimals: 18,
      name: 'CE<PERSON><PERSON>',
      symbol: 'CELO',
    },
    rpcUrls: {
      default: { http: ['https://forno.celo.org'] },
      infura: {
        http: ['https://celo-mainnet.infura.io/v3'],
      },
      public: {
        http: ['https://forno.celo.org'],
      },
    },
    blockExplorers: {
      default: {
        name: 'Celo Explorer',
        url: 'https://explorer.celo.org/mainnet',
      },
      etherscan: { name: 'CeloScan', url: 'https://celoscan.io' },
    },
    contracts: {
      multicall3: {
        address: '******************************************',
        blockCreated: 13112599,
      },
    },
    testnet: false,
  },
  {
    formatters: formattersCelo,
    serializers: serializersCelo,
  },
)
