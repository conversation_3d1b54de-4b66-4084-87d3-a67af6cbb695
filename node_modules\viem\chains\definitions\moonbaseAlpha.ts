import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const moonbaseAlpha = /*#__PURE__*/ define<PERSON>hain({
  id: 1287,
  name: 'Moonbase Alpha',
  network: 'moonbase-alpha',
  nativeCurrency: {
    decimals: 18,
    name: 'DE<PERSON>',
    symbol: 'DEV',
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.api.moonbase.moonbeam.network'],
      webSocket: ['wss://wss.api.moonbase.moonbeam.network'],
    },
    public: {
      http: ['https://rpc.api.moonbase.moonbeam.network'],
      webSocket: ['wss://wss.api.moonbase.moonbeam.network'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Moonscan',
      url: 'https://moonbase.moonscan.io',
    },
    etherscan: {
      name: 'Moonscan',
      url: 'https://moonbase.moonscan.io',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 1850686,
    },
  },
  testnet: true,
})
