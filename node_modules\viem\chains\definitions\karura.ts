import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const karura = /*#__PURE__*/ define<PERSON>hain({
  id: 686,
  name: '<PERSON><PERSON><PERSON>',
  network: 'karura',
  nativeCurrency: {
    name: '<PERSON><PERSON><PERSON>',
    symbol: '<PERSON><PERSON>',
    decimals: 18,
  },
  rpcUrls: {
    public: {
      http: ['https://eth-rpc-karura.aca-api.network'],
      webSocket: ['wss://eth-rpc-karura.aca-api.network'],
    },
    default: {
      http: ['https://eth-rpc-karura.aca-api.network'],
      webSocket: ['wss://eth-rpc-karura.aca-api.network'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Ka<PERSON>ra Blockscout',
      url: 'https://blockscout.karura.network',
    },
  },
  testnet: false,
})
