import axios from 'axios';
import type { 
  ArbitrageOpportunity, 
  TradeExecution, 
  SystemInfo, 
  OpportunityStats, 
  TradeStats,
  SimulationResult,
  ApiResponse 
} from '@/types';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002/api'; // Using simple API server temporarily

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Opportunities API
export const opportunitiesApi = {
  getAll: async (): Promise<ArbitrageOpportunity[]> => {
    const response = await api.get<ApiResponse<ArbitrageOpportunity[]>>('/opportunities');
    return response.data.data || [];
  },

  getById: async (id: number): Promise<ArbitrageOpportunity | null> => {
    try {
      const response = await api.get<ApiResponse<ArbitrageOpportunity>>(`/opportunities/${id}`);
      return response.data.data || null;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null;
      }
      throw error;
    }
  },

  getStats: async (): Promise<OpportunityStats> => {
    const response = await api.get<ApiResponse<OpportunityStats>>('/opportunities/stats/summary');
    return response.data.data!;
  },

  simulate: async (opportunityId: number, amountIn: number): Promise<SimulationResult> => {
    const response = await api.post<ApiResponse<SimulationResult>>('/opportunities/simulate', {
      opportunityId,
      amountIn,
    });
    return response.data.data!;
  },
};

// Trades API
export const tradesApi = {
  getAll: async (limit?: number): Promise<TradeExecution[]> => {
    const response = await api.get<ApiResponse<TradeExecution[]>>('/trades', {
      params: { limit },
    });
    return response.data.data || [];
  },

  getById: async (id: number): Promise<TradeExecution | null> => {
    try {
      const response = await api.get<ApiResponse<TradeExecution>>(`/trades/${id}`);
      return response.data.data || null;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null;
      }
      throw error;
    }
  },

  execute: async (opportunityId: number, amountIn: number): Promise<TradeExecution> => {
    const response = await api.post<ApiResponse<TradeExecution>>('/trades/execute', {
      opportunityId,
      amountIn,
    });
    return response.data.data!;
  },

  getStats: async (): Promise<TradeStats> => {
    const response = await api.get<ApiResponse<TradeStats>>('/trades/stats/summary');
    return response.data.data!;
  },

  getContractBalance: async (tokenAddress?: string): Promise<{ tokenAddress: string; balance: string; symbol: string }> => {
    const response = await api.get<ApiResponse<{ tokenAddress: string; balance: string; symbol: string }>>('/trades/contract/balance', {
      params: { token: tokenAddress },
    });
    return response.data.data!;
  },
};

// System API
export const systemApi = {
  getInfo: async (): Promise<SystemInfo> => {
    const response = await api.get<ApiResponse<SystemInfo>>('/system/info');
    return response.data.data!;
  },

  getHealth: async (): Promise<any> => {
    const response = await api.get('/health');
    return response.data;
  },

  getConfig: async (): Promise<Record<string, string>> => {
    const response = await api.get<ApiResponse<Record<string, string>>>('/config');
    return response.data.data || {};
  },

  updateConfig: async (key: string, value: string): Promise<void> => {
    await api.post<ApiResponse<void>>('/config', { key, value });
  },
};

// Error handling utility
export const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.response?.data?.error) {
    return error.response.data.error;
  }
  if (error.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
};

// Utility function to format currency
export const formatCurrency = (amount: number, decimals: number = 6): string => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(amount);
};

// Utility function to format percentage
export const formatPercentage = (value: number, decimals: number = 2): string => {
  return `${(value * 100).toFixed(decimals)}%`;
};

// Utility function to format time
export const formatTime = (timestamp: string): string => {
  if (!timestamp) {
    return 'N/A';
  }

  // Handle different timestamp formats
  let date: Date;

  // If it's a number (Unix timestamp), convert to milliseconds
  if (!isNaN(Number(timestamp))) {
    const num = Number(timestamp);
    // If it's less than 13 digits, it's likely in seconds, convert to milliseconds
    date = new Date(num < 10000000000000 ? num * 1000 : num);
  } else {
    // Try to parse as ISO string or other formats
    date = new Date(timestamp);
  }

  // Check if the date is valid
  if (isNaN(date.getTime())) {
    console.warn('Invalid timestamp received:', timestamp);
    return 'Invalid Date';
  }

  return date.toLocaleString();
};

// Utility function to format duration
export const formatDuration = (milliseconds: number): string => {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  }
  const seconds = Math.floor(milliseconds / 1000);
  if (seconds < 60) {
    return `${seconds}s`;
  }
  const minutes = Math.floor(seconds / 60);
  return `${minutes}m ${seconds % 60}s`;
};

export default api;
