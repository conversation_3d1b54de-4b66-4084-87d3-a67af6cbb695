import React, { useState } from 'react';
import { TrendingUp, AlertTriangle, Clock, Zap, ExternalLink, RefreshCw } from 'lucide-react';
import { ExecuteButton } from './ExecuteButton';
import { formatCurrency, formatPercentage, formatTime } from '@/lib/api';
import type { ArbitrageOpportunity, OpportunityStats, DataFreshness } from '@/types';

interface OpportunityListProps {
  opportunities: ArbitrageOpportunity[];
  onExecute: (opportunityId: number, amountIn: number) => Promise<void>;
  stats: OpportunityStats | null;
}

export const OpportunityList: React.FC<OpportunityListProps> = ({
  opportunities,
  onExecute,
  stats,
}) => {
  const [sortBy, setSortBy] = useState<'netProfit' | 'priceDifference' | 'detectedAt'>('netProfit');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const sortedOpportunities = [...opportunities].sort((a, b) => {
    const aValue = a[sortBy];
    const bValue = b[sortBy];
    
    if (sortBy === 'detectedAt') {
      const aTime = new Date(aValue as string).getTime();
      const bTime = new Date(bValue as string).getTime();
      return sortOrder === 'desc' ? bTime - aTime : aTime - bTime;
    }
    
    const aNum = Number(aValue);
    const bNum = Number(bValue);
    return sortOrder === 'desc' ? bNum - aNum : aNum - bNum;
  });

  const getRiskColor = (riskScore: number) => {
    if (riskScore <= 3) return 'text-success-600 bg-success-100';
    if (riskScore <= 6) return 'text-warning-600 bg-warning-100';
    return 'text-danger-600 bg-danger-100';
  };

  const getRiskLabel = (riskScore: number) => {
    if (riskScore <= 3) return 'Low';
    if (riskScore <= 6) return 'Medium';
    return 'High';
  };

  const getDexDisplayName = (dex: string) => {
    switch (dex) {
      case 'uniswap_v3': return 'Uniswap V3';
      case 'sushiswap': return 'SushiSwap';
      default: return dex;
    }
  };

  const getFreshnessColor = (status: string) => {
    switch (status) {
      case 'fresh': return 'text-green-600 bg-green-100';
      case 'aging': return 'text-yellow-600 bg-yellow-100';
      case 'stale': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getFreshnessIcon = (status: string) => {
    switch (status) {
      case 'fresh': return <Zap className="w-3 h-3" />;
      case 'aging': return <Clock className="w-3 h-3" />;
      case 'stale': return <AlertTriangle className="w-3 h-3" />;
      default: return <RefreshCw className="w-3 h-3" />;
    }
  };

  const formatDataAge = (ageInSeconds: number) => {
    if (ageInSeconds < 60) {
      return `${ageInSeconds}s`;
    } else if (ageInSeconds < 3600) {
      return `${Math.floor(ageInSeconds / 60)}m`;
    } else {
      return `${Math.floor(ageInSeconds / 3600)}h`;
    }
  };

  if (opportunities.length === 0) {
    return (
      <div className="card text-center py-12">
        <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Opportunities Available</h3>
        <p className="text-gray-600">
          Waiting for profitable arbitrage opportunities to be detected...
        </p>
      </div>
    );
  }

  const staleOpportunities = opportunities.filter(opp => opp.dataFreshness?.isStale);
  const agingOpportunities = opportunities.filter(opp => opp.dataFreshness?.status === 'aging');

  return (
    <div className="space-y-6">
      {/* Data Freshness Warning */}
      {(staleOpportunities.length > 0 || agingOpportunities.length > 0) && (
        <div className="card border-l-4 border-yellow-400 bg-yellow-50">
          <div className="flex items-start">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-yellow-800">Data Freshness Warning</h3>
              <div className="mt-1 text-sm text-yellow-700">
                {staleOpportunities.length > 0 && (
                  <p className="mb-1">
                    <strong>{staleOpportunities.length}</strong> opportunities have stale data (older than 5 minutes) and should not be executed.
                  </p>
                )}
                {agingOpportunities.length > 0 && (
                  <p>
                    <strong>{agingOpportunities.length}</strong> opportunities have aging data (older than 1 minute) - proceed with caution.
                  </p>
                )}
                <p className="mt-2 font-medium">
                  Always verify current market prices before executing any trades.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Stats Summary */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="card">
            <h3 className="text-sm font-medium text-gray-600 mb-2">Best Opportunity</h3>
            {stats.bestOpportunity ? (
              <div>
                <p className="text-lg font-bold text-gray-900">
                  {stats.bestOpportunity.symbolA}/{stats.bestOpportunity.symbolB}
                </p>
                <p className="text-sm text-success-600">
                  +{formatCurrency(stats.bestOpportunity.netProfit)} ETH
                </p>
              </div>
            ) : (
              <p className="text-gray-500">None available</p>
            )}
          </div>
          
          <div className="card">
            <h3 className="text-sm font-medium text-gray-600 mb-2">Average Profit</h3>
            <p className="text-lg font-bold text-gray-900">
              {formatCurrency(stats.averageProfit)} ETH
            </p>
          </div>
          
          <div className="card">
            <h3 className="text-sm font-medium text-gray-600 mb-2">Total Potential</h3>
            <p className="text-lg font-bold text-gray-900">
              {formatCurrency(stats.totalNetProfit)} ETH
            </p>
          </div>
        </div>
      )}

      {/* Opportunities Table */}
      <div className="card p-0">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              Active Opportunities ({opportunities.length})
            </h2>
            
            {/* Sort Controls */}
            <div className="flex items-center space-x-2">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="input text-sm py-1"
              >
                <option value="netProfit">Net Profit</option>
                <option value="priceDifference">Price Difference</option>
                <option value="detectedAt">Detection Time</option>
              </select>
              
              <button
                onClick={() => setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')}
                className="btn-secondary btn-sm"
              >
                {sortOrder === 'desc' ? '↓' : '↑'}
              </button>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="table">
            <thead>
              <tr>
                <th>Pair</th>
                <th>DEX Route</th>
                <th>Price Difference</th>
                <th>Net Profit</th>
                <th>Amount Range</th>
                <th>Risk</th>
                <th>Data Age</th>
                <th>Expires</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              {sortedOpportunities.map((opportunity) => (
                <tr key={opportunity.id || `${opportunity.tokenA}-${opportunity.tokenB}-${opportunity.dexA}-${opportunity.dexB}`}>
                  <td>
                    <div className="flex items-center">
                      <div>
                        <p className="font-medium text-gray-900">
                          {opportunity.symbolA}/{opportunity.symbolB}
                        </p>
                        <p className="text-sm text-gray-500">
                          {formatPercentage(opportunity.priceDifference)} spread
                        </p>
                      </div>
                    </div>
                  </td>
                  
                  <td>
                    <div className="text-sm">
                      <p className="text-gray-900">
                        {getDexDisplayName(opportunity.dexA)} → {getDexDisplayName(opportunity.dexB)}
                      </p>
                      <p className="text-gray-500">
                        {formatCurrency(opportunity.priceA, 4)} → {formatCurrency(opportunity.priceB, 4)}
                      </p>
                    </div>
                  </td>
                  
                  <td>
                    <span className="badge-success">
                      {formatPercentage(opportunity.priceDifference)}
                    </span>
                  </td>
                  
                  <td>
                    <div className="text-sm">
                      <p className="font-medium text-success-600">
                        +{formatCurrency(opportunity.netProfit)} ETH
                      </p>
                      <p className="text-gray-500">
                        Gas: {formatCurrency(opportunity.gasCost)} ETH
                      </p>
                    </div>
                  </td>
                  
                  <td>
                    <div className="text-sm text-gray-600">
                      <p>{formatCurrency(opportunity.minAmount, 2)} ETH</p>
                      <p>to {formatCurrency(opportunity.maxAmount, 2)} ETH</p>
                    </div>
                  </td>
                  
                  <td>
                    <span className={`badge ${getRiskColor(opportunity.riskScore)}`}>
                      {getRiskLabel(opportunity.riskScore)}
                    </span>
                  </td>

                  <td>
                    {opportunity.dataFreshness ? (
                      <div className="flex items-center">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getFreshnessColor(opportunity.dataFreshness.status)}`}>
                          {getFreshnessIcon(opportunity.dataFreshness.status)}
                          <span className="ml-1">{formatDataAge(opportunity.dataFreshness.ageInSeconds)}</span>
                        </span>
                        {opportunity.dataFreshness.warning && (
                          <div className="ml-2" title={opportunity.dataFreshness.warning}>
                            <AlertTriangle className="w-4 h-4 text-yellow-500" />
                          </div>
                        )}
                      </div>
                    ) : (
                      <span className="text-gray-400 text-sm">Unknown</span>
                    )}
                  </td>

                  <td>
                    <div className="flex items-center text-sm text-gray-600">
                      <Clock className="w-4 h-4 mr-1" />
                      {new Date(opportunity.expiresAt).toLocaleTimeString()}
                    </div>
                  </td>
                  
                  <td>
                    <ExecuteButton
                      opportunity={opportunity}
                      onExecute={onExecute}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};
