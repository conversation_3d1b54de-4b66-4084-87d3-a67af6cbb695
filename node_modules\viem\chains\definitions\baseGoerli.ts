import { define<PERSON>hain } from '../../utils/chain/defineChain.js'
import { formattersOptimism } from '../optimism/formatters.js'

export const baseGoerli = /*#__PURE__*/ define<PERSON>hain(
  {
    id: 84531,
    network: 'base-goerli',
    name: 'Base Goerli',
    nativeCurrency: { name: '<PERSON><PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
      alchemy: {
        http: ['https://base-goerli.g.alchemy.com/v2'],
        webSocket: ['wss://base-goerli.g.alchemy.com/v2'],
      },
      default: {
        http: ['https://goerli.base.org'],
      },
      public: {
        http: ['https://goerli.base.org'],
      },
    },
    blockExplorers: {
      etherscan: {
        name: 'Basescan',
        url: 'https://goerli.basescan.org',
      },
      default: {
        name: 'Basescan',
        url: 'https://goerli.basescan.org',
      },
    },
    contracts: {
      multicall3: {
        address: '******************************************',
        blockCreated: 1376988,
      },
    },
    testnet: true,
    sourceId: 5, // goerli
  },
  {
    formatters: formattersOptimism,
  },
)
