import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const holesky = /*#__PURE__*/ defineChain({
  id: 17000,
  network: 'holesky',
  name: '<PERSON><PERSON>',
  nativeCurrency: { name: '<PERSON><PERSON>ther', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://ethereum-holesky.publicnode.com'],
    },
    public: {
      http: ['https://ethereum-holesky.publicnode.com'],
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 77,
    },
  },
  testnet: true,
})
