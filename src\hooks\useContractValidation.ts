import { useWeb3 } from '@/contexts/Web3Context';
import { useCallback } from 'react';

export interface ContractValidationResult {
  isValid: boolean;
  error?: string;
  canExecute: boolean;
}

export const useContractValidation = () => {
  const { walletState, isContractReady, contractError, publicClient, walletClient } = useWeb3();

  const validateContractOperation = useCallback((): ContractValidationResult => {
    // Check if wallet is connected
    if (!walletState.isConnected) {
      return {
        isValid: false,
        error: 'Wallet not connected. Please connect your wallet to continue.',
        canExecute: false,
      };
    }

    // Check if there's a contract error
    if (contractError) {
      return {
        isValid: false,
        error: `Contract initialization error: ${contractError}`,
        canExecute: false,
      };
    }

    // Check if contract is ready
    if (!isContractReady) {
      return {
        isValid: false,
        error: 'Contract or signer not initialized. Please reconnect your wallet.',
        canExecute: false,
      };
    }

    // Check if clients are available
    if (!publicClient) {
      return {
        isValid: false,
        error: 'Blockchain client not initialized. Please refresh the page.',
        canExecute: false,
      };
    }

    if (!walletClient) {
      return {
        isValid: false,
        error: 'Wallet client not initialized. Please reconnect your wallet.',
        canExecute: false,
      };
    }

    // Check if user has sufficient balance for gas
    if (walletState.balance && parseFloat(walletState.balance) < 0.001) {
      return {
        isValid: true,
        error: 'Low ETH balance. You may not have enough for gas fees.',
        canExecute: true, // Still allow execution, just warn
      };
    }

    return {
      isValid: true,
      canExecute: true,
    };
  }, [walletState, isContractReady, contractError, publicClient, walletClient]);

  const validateArbitrageExecution = useCallback((amountIn: number): ContractValidationResult => {
    const baseValidation = validateContractOperation();
    
    if (!baseValidation.isValid) {
      return baseValidation;
    }

    // Additional validation for arbitrage execution
    if (amountIn <= 0) {
      return {
        isValid: false,
        error: 'Invalid amount. Amount must be greater than 0.',
        canExecute: false,
      };
    }

    // Check if user has enough balance for the trade
    if (walletState.balance && parseFloat(walletState.balance) < amountIn) {
      return {
        isValid: false,
        error: `Insufficient balance. You have ${walletState.balance} ETH but trying to trade ${amountIn} ETH.`,
        canExecute: false,
      };
    }

    return baseValidation;
  }, [validateContractOperation, walletState.balance]);

  return {
    validateContractOperation,
    validateArbitrageExecution,
    isWalletConnected: walletState.isConnected,
    isContractReady,
    contractError,
  };
};
