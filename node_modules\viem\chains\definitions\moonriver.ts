import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const moonriver = /*#__PURE__*/ defineChain({
  id: 1285,
  name: 'Moonriver',
  network: 'moonriver',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON><PERSON>',
    symbol: '<PERSON><PERSON><PERSON>',
  },
  rpcUrls: {
    public: {
      http: ['https://moonriver.public.blastapi.io'],
      webSocket: ['wss://moonriver.public.blastapi.io'],
    },
    default: {
      http: ['https://moonriver.public.blastapi.io'],
      webSocket: ['wss://moonriver.public.blastapi.io'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Moonscan',
      url: 'https://moonriver.moonscan.io',
    },
    etherscan: {
      name: 'Moonscan',
      url: 'https://moonriver.moonscan.io',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 1597904,
    },
  },
  testnet: false,
})
