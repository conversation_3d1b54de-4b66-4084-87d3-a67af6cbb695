import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const meterTestnet = /*#__PURE__*/ defineChain({
  id: 83,
  name: 'Meter Testnet',
  network: 'meter-testnet',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: '<PERSON><PERSON>',
  },
  rpcUrls: {
    default: { http: ['https://rpctest.meter.io'] },
    public: { http: ['https://rpctest.meter.io'] },
  },
  blockExplorers: {
    default: {
      name: 'MeterTestnetScan',
      url: 'https://scan-warringstakes.meter.io',
    },
  },
})
