import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { createPublicClient, createWalletClient, custom, http, getContract } from 'viem';
import { mainnet, sepolia } from 'viem/chains';
import type { WalletState } from '@/types';

interface Web3ContextType {
  walletState: WalletState;
  connectWallet: () => Promise<void>;
  disconnectWallet: () => void;
  publicClient: any;
  walletClient: any;
  isContractReady: boolean;
  contractError: string | null;
}

const Web3Context = createContext<Web3ContextType | undefined>(undefined);

interface Web3ProviderProps {
  children: ReactNode;
}

export const Web3Provider: React.FC<Web3ProviderProps> = ({ children }) => {
  const [walletState, setWalletState] = useState<WalletState>({
    isConnected: false,
    isConnecting: false,
  });
  
  const [publicClient, setPublicClient] = useState<any>(null);
  const [walletClient, setWalletClient] = useState<any>(null);
  const [isContractReady, setIsContractReady] = useState(false);
  const [contractError, setContractError] = useState<string | null>(null);

  // Initialize public client on mount
  useEffect(() => {
    try {
      const client = createPublicClient({
        chain: mainnet, // You can make this configurable
        transport: http(),
      });
      setPublicClient(client);
    } catch (error) {
      console.error('Failed to initialize public client:', error);
      setContractError('Failed to initialize blockchain client');
    }
  }, []);

  // Check for existing wallet connection on mount
  useEffect(() => {
    checkExistingConnection();
  }, []);

  const checkExistingConnection = async () => {
    if (typeof window.ethereum === 'undefined') {
      return;
    }

    try {
      const accounts = await window.ethereum.request({
        method: 'eth_accounts',
      });

      if (accounts.length > 0) {
        await connectWallet();
      }
    } catch (error) {
      console.error('Error checking existing connection:', error);
    }
  };

  const connectWallet = async () => {
    if (typeof window.ethereum === 'undefined') {
      setWalletState(prev => ({
        ...prev,
        error: 'Please install MetaMask to connect your wallet',
      }));
      return;
    }

    try {
      setWalletState(prev => ({ ...prev, isConnecting: true, error: undefined }));
      setContractError(null);

      // Request account access
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts',
      });

      if (accounts.length === 0) {
        throw new Error('No accounts found');
      }

      const account = accounts[0];

      // Get balance
      const balanceWei = await window.ethereum.request({
        method: 'eth_getBalance',
        params: [account, 'latest'],
      });
      
      const balanceEth = (parseInt(balanceWei, 16) / 1e18).toFixed(4);

      // Get chain ID
      const chainId = await window.ethereum.request({
        method: 'eth_chainId',
      });

      // Create wallet client
      const wallet = createWalletClient({
        chain: mainnet, // You can make this configurable based on chainId
        transport: custom(window.ethereum),
        account: account,
      });

      setWalletClient(wallet);
      setIsContractReady(true);

      setWalletState({
        isConnected: true,
        address: account,
        balance: balanceEth,
        chainId: parseInt(chainId, 16),
        isConnecting: false,
      });

      // Listen for account changes
      window.ethereum.on('accountsChanged', handleAccountsChanged);
      
      // Listen for chain changes
      window.ethereum.on('chainChanged', handleChainChanged);

    } catch (error: any) {
      console.error('Error connecting wallet:', error);
      setWalletState(prev => ({
        ...prev,
        isConnecting: false,
        error: error.message || 'Failed to connect wallet',
      }));
      setContractError('Failed to initialize wallet connection');
    }
  };

  const disconnectWallet = () => {
    // Remove event listeners
    if (window.ethereum) {
      window.ethereum.removeListener('accountsChanged', handleAccountsChanged);
      window.ethereum.removeListener('chainChanged', handleChainChanged);
    }

    setWalletClient(null);
    setIsContractReady(false);
    setContractError(null);
    
    setWalletState({
      isConnected: false,
      isConnecting: false,
    });
  };

  const handleAccountsChanged = (accounts: string[]) => {
    if (accounts.length === 0) {
      disconnectWallet();
    } else {
      setWalletState(prev => ({
        ...prev,
        address: accounts[0],
      }));
    }
  };

  const handleChainChanged = () => {
    // Reload the page when chain changes to avoid state inconsistencies
    window.location.reload();
  };

  const value: Web3ContextType = {
    walletState,
    connectWallet,
    disconnectWallet,
    publicClient,
    walletClient,
    isContractReady,
    contractError,
  };

  return (
    <Web3Context.Provider value={value}>
      {children}
    </Web3Context.Provider>
  );
};

export const useWeb3 = (): Web3ContextType => {
  const context = useContext(Web3Context);
  if (context === undefined) {
    throw new Error('useWeb3 must be used within a Web3Provider');
  }
  return context;
};

// Extend Window interface for TypeScript
declare global {
  interface Window {
    ethereum?: any;
  }
}
