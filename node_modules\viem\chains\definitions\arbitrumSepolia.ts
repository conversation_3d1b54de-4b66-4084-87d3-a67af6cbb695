import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const arbitrumSepolia = /*#__PURE__*/ define<PERSON>hain({
  id: 421_614,
  name: 'Arbitrum Sepolia',
  network: 'arbitrum-sepolia',
  nativeCurrency: {
    name: 'Arbitrum Sepolia Ether',
    symbol: 'ETH',
    decimals: 18,
  },
  rpcUrls: {
    alchemy: {
      http: ['https://arb-sepolia.g.alchemy.com/v2'],
      webSocket: ['wss://arb-sepolia.g.alchemy.com/v2'],
    },
    default: {
      http: ['https://sepolia-rollup.arbitrum.io/rpc'],
    },
    public: {
      http: ['https://sepolia-rollup.arbitrum.io/rpc'],
    },
  },
  blockExplorers: {
    etherscan: { name: 'Arbiscan', url: 'https://sepolia.arbiscan.io' },
    default: { name: 'Arbiscan', url: 'https://sepolia.arbiscan.io' },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 81930,
    },
  },
  testnet: true,
})
