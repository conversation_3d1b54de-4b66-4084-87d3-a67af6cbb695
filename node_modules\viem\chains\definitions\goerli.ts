import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const goerli = /*#__PURE__*/ defineChain({
  id: 5,
  network: 'goerli',
  name: '<PERSON><PERSON><PERSON>',
  nativeCurrency: { name: '<PERSON><PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    alchemy: {
      http: ['https://eth-goerli.g.alchemy.com/v2'],
      webSocket: ['wss://eth-goerli.g.alchemy.com/v2'],
    },
    infura: {
      http: ['https://goerli.infura.io/v3'],
      webSocket: ['wss://goerli.infura.io/ws/v3'],
    },
    default: {
      http: ['https://rpc.ankr.com/eth_goerli'],
    },
    public: {
      http: ['https://rpc.ankr.com/eth_goerli'],
    },
  },
  blockExplorers: {
    etherscan: {
      name: 'Etherscan',
      url: 'https://goerli.etherscan.io',
    },
    default: {
      name: 'Etherscan',
      url: 'https://goerli.etherscan.io',
    },
  },
  contracts: {
    ensRegistry: {
      address: '******************************************',
    },
    ensUniversalResolver: {
      address: '******************************************',
      blockCreated: 8765204,
    },
    multicall3: {
      address: '******************************************',
      blockCreated: 6507670,
    },
  },
  testnet: true,
})
